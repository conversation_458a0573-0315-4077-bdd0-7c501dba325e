{"@timestamp":"2025-08-03T03:25:51.985+08:00","@version":"1","message":"Application started with classpath: [file:/Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin/target/classes/, file:/Users/<USER>/.m2/repository/com/genco/genco-service/0.0.1-SNAPSHOT/genco-service-0.0.1-SNAPSHOT.jar, file:/Users/<USER>/.m2/repository/com/genco/genco-common/0.0.1-SNAPSHOT/genco-common-0.0.1-SNAPSHOT.jar, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/Users/<USER>/.m2/repository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/Users/<USER>/.m2/repository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/Users/<USER>/.m2/repository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/Users/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.0/mybatis-spring-boot-starter-2.2.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.0/mybatis-spring-boot-autoconfigure-2.2.0.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar, file:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/Users/<USER>/.m2/repository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar]","logger_name":"org.springframework.boot.context.logging.ClasspathLoggingApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:52.025+08:00","@version":"1","message":"Starting GencoAdminApplication on mgc-mini.local with PID 41538 (/Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin/target/classes started by mgcis in /Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin)","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.025+08:00","@version":"1","message":"The following profiles are active: dev","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.025+08:00","@version":"1","message":"Loading source class com.genco.admin.GencoAdminApplication","logger_name":"org.springframework.boot.SpringApplication","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:52.047+08:00","@version":"1","message":"Activated activeProfiles dev","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:52.047+08:00","@version":"1","message":"Profiles already activated, '[prod]' will not be applied","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:52.047+08:00","@version":"1","message":"Loaded config file 'file:/Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin/target/classes/application.yml' (classpath:/application.yml)","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:52.047+08:00","@version":"1","message":"Profiles already activated, '[dev]' will not be applied","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:52.047+08:00","@version":"1","message":"Loaded config file 'file:/Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:52.048+08:00","@version":"1","message":"Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6b53bcc2","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:52.585+08:00","@version":"1","message":"Multiple Spring Data modules found, entering strict repository configuration mode!","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.586+08:00","@version":"1","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.600+08:00","@version":"1","message":"Finished Spring Data repository scanning in 8ms. Found 0 Redis repository interfaces.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.856+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.860+08:00","@version":"1","message":"Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.861+08:00","@version":"1","message":"Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@4afcae7' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.862+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.865+08:00","@version":"1","message":"Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.976+08:00","@version":"1","message":"Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:52.976+08:00","@version":"1","message":"Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:52.976+08:00","@version":"1","message":"None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:52.987+08:00","@version":"1","message":"Tomcat initialized with port(s): 20010 (http)","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.991+08:00","@version":"1","message":"Initializing ProtocolHandler [\"http-nio-20010\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.991+08:00","@version":"1","message":"Starting service [Tomcat]","logger_name":"org.apache.catalina.core.StandardService","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:52.991+08:00","@version":"1","message":"Starting Servlet engine: [Apache Tomcat/9.0.33]","logger_name":"org.apache.catalina.core.StandardEngine","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:53.088+08:00","@version":"1","message":"At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.","logger_name":"org.apache.jasper.servlet.TldScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:53.090+08:00","@version":"1","message":"Initializing Spring embedded WebApplicationContext","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:53.090+08:00","@version":"1","message":"Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:53.090+08:00","@version":"1","message":"Root WebApplicationContext: initialization completed in 1042 ms","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:53.388+08:00","@version":"1","message":"Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:53.388+08:00","@version":"1","message":"Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:53.397+08:00","@version":"1","message":"Filter 'webMvcMetricsFilter' configured for use","logger_name":"org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:53.397+08:00","@version":"1","message":"Filter 'requestContextFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:53.397+08:00","@version":"1","message":"Filter 'corsFilter' configured for use","logger_name":"org.springframework.web.filter.CorsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:53.398+08:00","@version":"1","message":"Filter 'characterEncodingFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:53.398+08:00","@version":"1","message":"Filter 'springSecurityFilterChain' configured for use","logger_name":"org.springframework.boot.web.servlet.DelegatingFilterProxyRegistrationBean$1","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:53.398+08:00","@version":"1","message":"Filter 'formContentFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedFormContentFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:54.993+08:00","@version":"1","message":"支付策略初始化完成，支持的支付类型: [offline, weixin, alipay, xendit, haipay, yue]","logger_name":"com.genco.service.service.impl.PaymentStrategyFactory","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:55.468+08:00","@version":"1","message":"289 mappings in 'requestMappingHandlerMapping'","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:55.500+08:00","@version":"1","message":"Exposing 2 endpoint(s) beneath base path '/actuator'","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:55.548+08:00","@version":"1","message":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","logger_name":"springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:55.563+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:55.563+08:00","@version":"1","message":"Initializing ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:55.620+08:00","@version":"1","message":"Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7cf166db, org.springframework.security.web.context.SecurityContextPersistenceFilter@38991781, org.springframework.security.web.header.HeaderWriterFilter@620e0d36, org.springframework.web.filter.CorsFilter@75de7009, org.springframework.web.filter.CorsFilter@75de7009, org.springframework.web.filter.CorsFilter@75de7009, org.springframework.security.web.authentication.logout.LogoutFilter@44a7661d, com.genco.admin.filter.JwtAuthenticationTokenFilter@13c18bba, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@57c7ec30, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2821da0a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@68303aad, org.springframework.security.web.session.SessionManagementFilter@58267ba1, org.springframework.security.web.access.ExceptionTranslationFilter@45649467, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4a7427f9]","logger_name":"org.springframework.security.web.DefaultSecurityFilterChain","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:55.692+08:00","@version":"1","message":"ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:55.724+08:00","@version":"1","message":"Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:55.734+08:00","@version":"1","message":"ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:25:55.929+08:00","@version":"1","message":"Context refreshed","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:55.935+08:00","@version":"1","message":"Found 2 custom documentation plugin(s)","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:55.947+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.026+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.029+08:00","@version":"1","message":"Generating unique operation named: getByIdsUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.031+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.035+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.037+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.038+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.048+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.049+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.056+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.057+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.063+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.076+08:00","@version":"1","message":"Generating unique operation named: updatePhoneUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.080+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.088+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.091+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.092+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.093+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.094+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.095+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.096+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.099+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.109+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.116+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.119+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.120+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.128+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.134+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.136+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.138+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.139+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.139+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.149+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.152+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.152+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.155+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.158+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.163+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.173+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.180+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.184+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.198+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.204+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.208+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.209+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.210+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.213+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.222+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.232+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.233+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.235+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.240+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.244+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.244+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.247+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.248+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.256+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.256+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.258+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.259+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.259+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.260+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.261+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.262+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.263+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.264+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.265+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.265+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.267+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.268+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.269+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.270+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.271+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.273+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.280+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.281+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.281+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.283+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.284+08:00","@version":"1","message":"Generating unique operation named: getListTreeUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.285+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.286+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.287+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.289+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.293+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.293+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.294+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.294+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.295+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.296+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.297+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.298+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.298+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.299+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.301+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.301+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.302+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.302+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.303+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.306+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.307+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.307+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.309+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.310+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.311+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.312+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.314+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.315+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.315+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.316+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.316+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.317+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.318+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_29","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.319+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.320+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.321+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.321+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.322+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.323+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_30","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.324+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.325+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.325+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.326+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.326+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.327+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_31","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.328+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.328+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.344+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_32","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.345+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.348+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.354+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_33","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.355+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.355+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.356+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.357+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_34","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.357+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.358+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.358+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.360+08:00","@version":"1","message":"Generating unique operation named: getListUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.361+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_35","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.362+08:00","@version":"1","message":"Generating unique operation named: balanceUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.364+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_36","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.364+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.365+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_37","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.365+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.366+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.366+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.372+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.381+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.383+08:00","@version":"1","message":"---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Sun Aug 03 03:25:56 CST 2025","logger_name":"com.genco.admin.task.tiktok.TiktokTokenRefreshTask","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.383+08:00","@version":"1","message":"Starting ProtocolHandler [\"http-nio-20010\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.403+08:00","@version":"1","message":"Tomcat started on port(s): 20010 (http) with context path ''","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.404+08:00","@version":"1","message":"Started GencoAdminApplication in 4.625 seconds (JVM running for 4.846)","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:56.432+08:00","@version":"1","message":"{dataSource-1} inited","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:57.717+08:00","@version":"1","message":"Tiktok access_token 未到刷新时间，当前时间: 1754162756，过期时间: 1781838424","logger_name":"com.genco.admin.task.tiktok.TiktokTokenRefreshTask","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:25:58.093+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:25:57 CST 2025 至 Sun Aug 03 03:25:57 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:26:58.687+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:26:58 CST 2025 至 Sun Aug 03 03:26:58 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:45.805+08:00","@version":"1","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:45.808+08:00","@version":"1","message":"Initializing Servlet 'dispatcherServlet'","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:45.809+08:00","@version":"1","message":"Detected StandardServletMultipartResolver","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:27:45.839+08:00","@version":"1","message":"enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:27:45.839+08:00","@version":"1","message":"Completed initialization in 31 ms","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:45.853+08:00","@version":"1","message":"Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]","thread_name":"http-nio-20010-exec-1","level":"ERROR","level_value":40000,"stack_trace":"org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String \"//\"\n\tat org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)\n\tat org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)\n\tat org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)\n\tat org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)\n\tat org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)\n\tat org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)\n\tat org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)\n\tat org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)\n\tat org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)\n\tat org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)\n\tat org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)\n\tat org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\n\tat java.lang.Thread.run(Thread.java:750)\n"}
{"@timestamp":"2025-08-03T03:27:45.869+08:00","@version":"1","message":"GET \"/api/admin/validate/code/get?temp=**********\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:27:45.869+08:00","@version":"1","message":"GET \"/api/admin/getLoginPic?temp=**********\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:27:45.875+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:27:45.875+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.ValidateCodeController#get()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:27:45.875+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:45.875+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:45.897+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:45.897+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:46.281+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：372208625 ns，cost：372 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:46.288+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：380388375 ns，cost：380 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:46.290+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:27:46.290+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:27:46.290+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@70c5653a]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:27:46.290+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@3023475d]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:27:46.293+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:46.293+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:46.293+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:27:46.296+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:46.296+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:46.296+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:27:59.058+08:00","@version":"1","message":"开始同步TikTok订单 - pageToken: null, startTime: Thu Jul 31 03:26:59 CST 2025, endTime: Sun Aug 03 03:26:59 CST 2025","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:27:59.785+08:00","@version":"1","message":"开始调用TikTok API获取订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:28:00.716+08:00","@version":"1","message":"API调用成功，获取到下一页token: , 开始处理订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:28:00.782+08:00","@version":"1","message":"开始处理 0 个订单","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:28:00.782+08:00","@version":"1","message":"订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:28:00.910+08:00","@version":"1","message":"TikTok订单同步事务执行完成，下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:28:00.911+08:00","@version":"1","message":"TikTok订单同步方法执行完成，返回下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:29:01.434+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:29:01 CST 2025 至 Sun Aug 03 03:29:01 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-8","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:30:02.037+08:00","@version":"1","message":"开始同步TikTok订单 - pageToken: null, startTime: Thu Jul 31 03:29:01 CST 2025, endTime: Sun Aug 03 03:29:01 CST 2025","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:30:02.252+08:00","@version":"1","message":"开始调用TikTok API获取订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:30:02.466+08:00","@version":"1","message":"API调用成功，获取到下一页token: , 开始处理订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:30:02.532+08:00","@version":"1","message":"开始处理 0 个订单","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:30:02.532+08:00","@version":"1","message":"订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:30:02.660+08:00","@version":"1","message":"TikTok订单同步事务执行完成，下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:30:02.661+08:00","@version":"1","message":"TikTok订单同步方法执行完成，返回下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:31:03.177+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:31:03 CST 2025 至 Sun Aug 03 03:31:03 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:32:03.545+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:32:03 CST 2025 至 Sun Aug 03 03:32:03 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:33:03.923+08:00","@version":"1","message":"开始同步TikTok订单 - pageToken: null, startTime: Thu Jul 31 03:32:03 CST 2025, endTime: Sun Aug 03 03:32:03 CST 2025","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:33:04.132+08:00","@version":"1","message":"开始调用TikTok API获取订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:33:04.324+08:00","@version":"1","message":"API调用成功，获取到下一页token: , 开始处理订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:33:04.389+08:00","@version":"1","message":"开始处理 0 个订单","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:33:04.390+08:00","@version":"1","message":"订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:33:04.518+08:00","@version":"1","message":"TikTok订单同步事务执行完成，下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:33:04.518+08:00","@version":"1","message":"TikTok订单同步方法执行完成，返回下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-14","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:34:05.045+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:34:04 CST 2025 至 Sun Aug 03 03:34:04 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-8","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:35:05.453+08:00","@version":"1","message":"开始同步TikTok订单 - pageToken: null, startTime: Thu Jul 31 03:34:05 CST 2025, endTime: Sun Aug 03 03:34:05 CST 2025","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:35:05.664+08:00","@version":"1","message":"开始调用TikTok API获取订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:35:05.853+08:00","@version":"1","message":"API调用成功，获取到下一页token: , 开始处理订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:35:06.145+08:00","@version":"1","message":"开始处理 0 个订单","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:35:06.145+08:00","@version":"1","message":"订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:35:06.273+08:00","@version":"1","message":"TikTok订单同步事务执行完成，下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:35:06.273+08:00","@version":"1","message":"TikTok订单同步方法执行完成，返回下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:35:57.727+08:00","@version":"1","message":"---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Sun Aug 03 03:35:57 CST 2025","logger_name":"com.genco.admin.task.tiktok.TiktokTokenRefreshTask","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:35:57.813+08:00","@version":"1","message":"Tiktok access_token 未到刷新时间，当前时间: 1754163357，过期时间: 1781838424","logger_name":"com.genco.admin.task.tiktok.TiktokTokenRefreshTask","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:36:06.758+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:36:06 CST 2025 至 Sun Aug 03 03:36:06 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-19","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:37:07.133+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:37:06 CST 2025 至 Sun Aug 03 03:37:06 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:38:07.507+08:00","@version":"1","message":"开始同步TikTok订单 - pageToken: null, startTime: Thu Jul 31 03:37:07 CST 2025, endTime: Sun Aug 03 03:37:07 CST 2025","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-22","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:38:07.780+08:00","@version":"1","message":"开始调用TikTok API获取订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-22","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:38:07.965+08:00","@version":"1","message":"API调用成功，获取到下一页token: , 开始处理订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-22","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:38:08.266+08:00","@version":"1","message":"开始处理 0 个订单","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-22","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:38:08.266+08:00","@version":"1","message":"订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-22","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:38:08.393+08:00","@version":"1","message":"TikTok订单同步事务执行完成，下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-22","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:38:08.394+08:00","@version":"1","message":"TikTok订单同步方法执行完成，返回下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-22","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:39:08.898+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:39:08 CST 2025 至 Sun Aug 03 03:39:08 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-24","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:40:09.302+08:00","@version":"1","message":"开始同步TikTok订单 - pageToken: null, startTime: Thu Jul 31 03:39:09 CST 2025, endTime: Sun Aug 03 03:39:09 CST 2025","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-13","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:40:09.511+08:00","@version":"1","message":"开始调用TikTok API获取订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-13","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:40:10.085+08:00","@version":"1","message":"API调用成功，获取到下一页token: , 开始处理订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-13","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:40:10.151+08:00","@version":"1","message":"开始处理 0 个订单","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-13","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:40:10.151+08:00","@version":"1","message":"订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-13","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:40:10.282+08:00","@version":"1","message":"TikTok订单同步事务执行完成，下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-13","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:40:10.282+08:00","@version":"1","message":"TikTok订单同步方法执行完成，返回下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-13","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:41:11.078+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:41:10 CST 2025 至 Sun Aug 03 03:41:10 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-27","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:42:11.439+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:42:11 CST 2025 至 Sun Aug 03 03:42:11 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-4","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:43:12.316+08:00","@version":"1","message":"开始同步TikTok订单 - pageToken: null, startTime: Thu Jul 31 03:42:11 CST 2025, endTime: Sun Aug 03 03:42:11 CST 2025","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-30","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:43:12.528+08:00","@version":"1","message":"开始调用TikTok API获取订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-30","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:43:12.914+08:00","@version":"1","message":"API调用成功，获取到下一页token: , 开始处理订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-30","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:43:12.978+08:00","@version":"1","message":"开始处理 0 个订单","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-30","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:43:12.978+08:00","@version":"1","message":"订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-30","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:43:13.107+08:00","@version":"1","message":"TikTok订单同步事务执行完成，下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-30","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:43:13.107+08:00","@version":"1","message":"TikTok订单同步方法执行完成，返回下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-30","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:44:13.621+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:44:13 CST 2025 至 Sun Aug 03 03:44:13 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:44:31.643+08:00","@version":"1","message":"Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6b53bcc2, started on Sun Aug 03 03:25:52 CST 2025","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"SpringContextShutdownHook","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:44:31.661+08:00","@version":"1","message":"Shutting down ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:44:31.667+08:00","@version":"1","message":"{dataSource-1} closing ...","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:44:31.670+08:00","@version":"1","message":"{dataSource-1} closed","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:10.578+08:00","@version":"1","message":"Application started with classpath: [file:/Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin/target/classes/, file:/Users/<USER>/.m2/repository/com/genco/genco-service/0.0.1-SNAPSHOT/genco-service-0.0.1-SNAPSHOT.jar, file:/Users/<USER>/.m2/repository/com/genco/genco-common/0.0.1-SNAPSHOT/genco-common-0.0.1-SNAPSHOT.jar, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/Users/<USER>/.m2/repository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/Users/<USER>/.m2/repository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/Users/<USER>/.m2/repository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/Users/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.0/mybatis-spring-boot-starter-2.2.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.0/mybatis-spring-boot-autoconfigure-2.2.0.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar, file:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/Users/<USER>/.m2/repository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar]","logger_name":"org.springframework.boot.context.logging.ClasspathLoggingApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:10.611+08:00","@version":"1","message":"Starting GencoAdminApplication on mgc-mini.local with PID 46122 (/Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin/target/classes started by mgcis in /Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin)","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:10.611+08:00","@version":"1","message":"The following profiles are active: dev","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:10.611+08:00","@version":"1","message":"Loading source class com.genco.admin.GencoAdminApplication","logger_name":"org.springframework.boot.SpringApplication","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:10.630+08:00","@version":"1","message":"Activated activeProfiles dev","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:10.630+08:00","@version":"1","message":"Profiles already activated, '[prod]' will not be applied","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:10.630+08:00","@version":"1","message":"Loaded config file 'file:/Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin/target/classes/application.yml' (classpath:/application.yml)","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:10.630+08:00","@version":"1","message":"Profiles already activated, '[dev]' will not be applied","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:10.630+08:00","@version":"1","message":"Loaded config file 'file:/Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:10.631+08:00","@version":"1","message":"Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7e8dcdaa","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:11.111+08:00","@version":"1","message":"Multiple Spring Data modules found, entering strict repository configuration mode!","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.111+08:00","@version":"1","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.125+08:00","@version":"1","message":"Finished Spring Data repository scanning in 8ms. Found 0 Redis repository interfaces.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.365+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.368+08:00","@version":"1","message":"Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.369+08:00","@version":"1","message":"Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@30358dc7' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.370+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.373+08:00","@version":"1","message":"Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.473+08:00","@version":"1","message":"Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:11.473+08:00","@version":"1","message":"Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:11.473+08:00","@version":"1","message":"None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:11.483+08:00","@version":"1","message":"Tomcat initialized with port(s): 20010 (http)","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.486+08:00","@version":"1","message":"Initializing ProtocolHandler [\"http-nio-20010\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.487+08:00","@version":"1","message":"Starting service [Tomcat]","logger_name":"org.apache.catalina.core.StandardService","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.487+08:00","@version":"1","message":"Starting Servlet engine: [Apache Tomcat/9.0.33]","logger_name":"org.apache.catalina.core.StandardEngine","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.571+08:00","@version":"1","message":"At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.","logger_name":"org.apache.jasper.servlet.TldScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.574+08:00","@version":"1","message":"Initializing Spring embedded WebApplicationContext","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.574+08:00","@version":"1","message":"Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:11.574+08:00","@version":"1","message":"Root WebApplicationContext: initialization completed in 943 ms","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:11.840+08:00","@version":"1","message":"Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:11.840+08:00","@version":"1","message":"Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:11.847+08:00","@version":"1","message":"Filter 'webMvcMetricsFilter' configured for use","logger_name":"org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:11.848+08:00","@version":"1","message":"Filter 'requestContextFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:11.848+08:00","@version":"1","message":"Filter 'corsFilter' configured for use","logger_name":"org.springframework.web.filter.CorsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:11.848+08:00","@version":"1","message":"Filter 'characterEncodingFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:11.848+08:00","@version":"1","message":"Filter 'springSecurityFilterChain' configured for use","logger_name":"org.springframework.boot.web.servlet.DelegatingFilterProxyRegistrationBean$1","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:11.848+08:00","@version":"1","message":"Filter 'formContentFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedFormContentFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:13.400+08:00","@version":"1","message":"支付策略初始化完成，支持的支付类型: [offline, weixin, alipay, xendit, haipay, yue]","logger_name":"com.genco.service.service.impl.PaymentStrategyFactory","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:13.774+08:00","@version":"1","message":"289 mappings in 'requestMappingHandlerMapping'","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:13.802+08:00","@version":"1","message":"Exposing 2 endpoint(s) beneath base path '/actuator'","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:13.847+08:00","@version":"1","message":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","logger_name":"springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:13.865+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:13.865+08:00","@version":"1","message":"Initializing ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:13.920+08:00","@version":"1","message":"Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@26a004ed, org.springframework.security.web.context.SecurityContextPersistenceFilter@70c99e13, org.springframework.security.web.header.HeaderWriterFilter@463746eb, org.springframework.web.filter.CorsFilter@3e595da3, org.springframework.web.filter.CorsFilter@3e595da3, org.springframework.web.filter.CorsFilter@3e595da3, org.springframework.security.web.authentication.logout.LogoutFilter@4a7427f9, com.genco.admin.filter.JwtAuthenticationTokenFilter@4acc27fd, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6a9f8c2f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6d514259, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2b6a4da7, org.springframework.security.web.session.SessionManagementFilter@6d96a895, org.springframework.security.web.access.ExceptionTranslationFilter@28ffa527, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1c96bf1e]","logger_name":"org.springframework.security.web.DefaultSecurityFilterChain","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:13.984+08:00","@version":"1","message":"ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:14.012+08:00","@version":"1","message":"Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:14.021+08:00","@version":"1","message":"ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:14.195+08:00","@version":"1","message":"Context refreshed","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.200+08:00","@version":"1","message":"Found 2 custom documentation plugin(s)","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.212+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.284+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.287+08:00","@version":"1","message":"Generating unique operation named: getByIdsUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.290+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.293+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.295+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.296+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.305+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.306+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.312+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.313+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.319+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.331+08:00","@version":"1","message":"Generating unique operation named: updatePhoneUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.334+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.342+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.344+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.345+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.346+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.347+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.347+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.348+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.351+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.361+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.366+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.369+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.369+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.377+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.383+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.385+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.387+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.387+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.388+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.397+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.399+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.400+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.403+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.404+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.410+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.419+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.424+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.428+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.441+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.447+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.451+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.451+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.452+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.455+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.457+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.458+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.458+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.460+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.461+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.461+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.462+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.464+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.464+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.470+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.471+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.472+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.472+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.473+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.473+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.475+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.475+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.476+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.477+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.477+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.478+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.479+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.480+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.481+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.482+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.483+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.485+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.493+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.494+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.495+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.497+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.498+08:00","@version":"1","message":"Generating unique operation named: getListTreeUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.499+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.500+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.500+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.504+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.508+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.508+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.508+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.509+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.509+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.511+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.511+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.512+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.513+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.513+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.515+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.515+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.516+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.516+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.518+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.520+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.520+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.521+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.522+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.524+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.525+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.526+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.527+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.528+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.529+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.529+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.529+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.530+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.532+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_29","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.533+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.534+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.534+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.534+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.535+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.537+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_30","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.538+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.538+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.539+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.539+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.539+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.540+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_31","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.541+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.541+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.556+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_32","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.557+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.560+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.566+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_33","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.567+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.568+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.568+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.569+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_34","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.569+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.570+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.570+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.572+08:00","@version":"1","message":"Generating unique operation named: getListUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.573+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_35","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.574+08:00","@version":"1","message":"Generating unique operation named: balanceUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.576+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_36","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.576+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.577+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_37","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.577+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.578+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.578+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.584+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.592+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.594+08:00","@version":"1","message":"---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Sun Aug 03 03:47:14 CST 2025","logger_name":"com.genco.admin.task.tiktok.TiktokTokenRefreshTask","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.595+08:00","@version":"1","message":"Starting ProtocolHandler [\"http-nio-20010\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.625+08:00","@version":"1","message":"Tomcat started on port(s): 20010 (http) with context path ''","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.627+08:00","@version":"1","message":"Started GencoAdminApplication in 4.25 seconds (JVM running for 4.464)","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:14.655+08:00","@version":"1","message":"{dataSource-1} inited","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:16.256+08:00","@version":"1","message":"Tiktok access_token 未到刷新时间，当前时间: 1754164034，过期时间: 1781838424","logger_name":"com.genco.admin.task.tiktok.TiktokTokenRefreshTask","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:16.566+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:47:16 CST 2025 至 Sun Aug 03 03:47:16 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:45.772+08:00","@version":"1","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:45.773+08:00","@version":"1","message":"Initializing Servlet 'dispatcherServlet'","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:45.773+08:00","@version":"1","message":"Detected StandardServletMultipartResolver","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:45.784+08:00","@version":"1","message":"enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:45.784+08:00","@version":"1","message":"Completed initialization in 11 ms","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:45.792+08:00","@version":"1","message":"Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]","thread_name":"http-nio-20010-exec-1","level":"ERROR","level_value":40000,"stack_trace":"org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String \"//\"\n\tat org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlacklistedUrls(StrictHttpFirewall.java:369)\n\tat org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:336)\n\tat org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:194)\n\tat org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:178)\n\tat org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:358)\n\tat org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:271)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:109)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)\n\tat org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)\n\tat org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)\n\tat org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)\n\tat org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)\n\tat org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)\n\tat org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)\n\tat org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)\n\tat org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)\n\tat org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)\n\tat org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)\n\tat org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\n\tat org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)\n\tat org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1594)\n\tat org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\n\tat java.lang.Thread.run(Thread.java:750)\n"}
{"@timestamp":"2025-08-03T03:47:45.800+08:00","@version":"1","message":"GET \"/api/admin/validate/code/get?temp=**********\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:45.800+08:00","@version":"1","message":"GET \"/api/admin/getLoginPic?temp=**********\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:45.803+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.ValidateCodeController#get()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:45.803+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.AdminLoginController#getLoginPic()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:45.804+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:45.804+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:45.816+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:45.816+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:46.153+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.ValidateCodeController.get()，prams：[]，cost time：330168917 ns，cost：330 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:46.161+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:46.162+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@64320588]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:46.163+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:46.164+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:46.164+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:46.522+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getLoginPic()，prams：[]，cost time：699364333 ns，cost：699 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:46.522+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:46.522+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@4aaa40cc]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:46.527+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:46.527+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:46.528+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:51.480+08:00","@version":"1","message":"POST \"/api/admin/login\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:51.481+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:51.481+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:51.481+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:51.499+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=d6e17f93e4fa90637bf34da0ccc2f2dd, code= (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.192+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=BzX36YjvNR8o, key=d6e17f93e4fa90637bf34da0ccc2f2dd, code=6bhf), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@634fd37]]，cost time：********* ns，cost：665 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.192+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.192+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@7676f2ad]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.193+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.194+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.194+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.214+08:00","@version":"1","message":"GET \"/api/admin/system/store/staff/list?page=1&limit=9999&temp=1754164072\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.214+08:00","@version":"1","message":"GET \"/api/admin/getAdminInfoByToken?token=aa66b074c5144399a7503a5b2431d2c8&temp=1754164072\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.214+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.SystemStoreStaffController#getList(Integer, PageParamRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.215+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.AdminLoginController#getAdminInfo()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.217+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-9","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.217+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-9","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.218+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-8","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.218+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-8","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.225+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getAdminInfo()，prams：[]，cost time：4873875 ns，cost：4 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-8","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.226+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.226+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@bfb34d3]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.228+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-8","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.229+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-8","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.229+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.234+08:00","@version":"1","message":"GET \"/api/admin/getMenus?temp=1754164072\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-11","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.234+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.AdminLoginController#getMenus()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-11","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.235+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.235+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.295+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.SystemStoreStaffController.getList(Integer,PageParamRequest)，prams：[0, PageParamRequest(page=1, limit=9999)]，cost time：70962833 ns，cost：70 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-9","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.295+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.295+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@3b83cb60]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.297+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-9","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.297+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-9","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.297+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.324+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.AdminLoginController.getMenus()，prams：[]，cost time：87979416 ns，cost：87 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.324+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-11","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.324+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@613ee47c]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-11","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.326+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.326+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-11","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.326+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-11","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.355+08:00","@version":"1","message":"GET \"/api/admin/system/config/info?formId=100&temp=1754164072\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-16","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.356+08:00","@version":"1","message":"GET \"/api/admin/system/config/getuniq?key=site_logo_square&temp=1754164072\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-17","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.357+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-16","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.357+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-17","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.358+08:00","@version":"1","message":"GET \"/api/admin/system/config/getuniq?key=site_logo_lefttop&temp=1754164072\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-15","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.358+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.SystemConfigController#justGetUniq(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-15","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.361+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.361+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-16","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.361+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.361+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-16","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.362+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-15","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.362+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-15","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.433+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_lefttop]，cost time：70181459 ns，cost：70 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-15","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.433+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-15","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.433+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@2d3bf40c]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-15","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.434+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.SystemConfigController.justGetUniq(String)，prams：[site_logo_square]，cost time：71991541 ns，cost：71 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.434+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-17","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.434+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@3e706838]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-17","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.435+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-15","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.435+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-15","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.435+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-15","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.435+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.435+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-17","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.435+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-17","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.499+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[100]，cost time：137555250 ns，cost：137 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-16","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.500+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-16","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.500+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@30400ac9]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-16","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:52.501+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-16","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.501+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-16","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:52.501+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-16","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:57.639+08:00","@version":"1","message":"GET \"/api/admin/system/config/info?formId=144&temp=1754164077\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-19","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:57.640+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.SystemConfigController#info(Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-19","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:57.641+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-19","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:57.642+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-19","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:57.711+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.SystemConfigController.info(Integer)，prams：[144]，cost time：68702334 ns，cost：68 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-19","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:57.711+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-19","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:57.711+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@32006849]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-19","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:47:57.712+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-19","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:57.712+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-19","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:47:57.712+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-19","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:48:04.918+08:00","@version":"1","message":"POST \"/api/admin/affiliate/products/search\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-21","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:48:04.918+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.AffiliateProductController#searchProducts(AffiliateProductSearchRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-20010-exec-21","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:48:04.920+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-21","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:04.920+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-21","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:04.923+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [AffiliateProductSearchRequest(pageSize=20, cursor=null, sortField=commission_rate, sortOrder=DESC, t (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-21","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:48:04.932+08:00","@version":"1","message":"联盟产品搜索请求 - 页面大小: 20, 关键词: null, 类目: null","logger_name":"com.genco.admin.controller.AffiliateProductController","thread_name":"http-nio-20010-exec-21","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:04.932+08:00","@version":"1","message":"调用AffiliateProductService进行联盟产品搜索","logger_name":"com.genco.admin.controller.AffiliateProductController","thread_name":"http-nio-20010-exec-21","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:04.932+08:00","@version":"1","message":"开始联盟选品查询，请求参数: AffiliateProductSearchRequest(pageSize=20, cursor=null, sortField=commission_rate, sortOrder=DESC, titleKeywords=null, salesPriceMin=null, salesPriceMax=null, salesPriceCurrency=null, categoryId=null, commissionRateMin=null, commissionRateMax=null, hasInventory=null, saleRegion=null, unitsSoldMin=null, unitsSoldMax=null)","logger_name":"com.genco.service.service.impl.AffiliateProductServiceImpl","thread_name":"http-nio-20010-exec-21","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:05.419+08:00","@version":"1","message":"调用TikTok联盟选品API，pageSize: 20, sortField: commission_rate, sortOrder: DESC","logger_name":"com.genco.service.service.impl.AffiliateProductServiceImpl","thread_name":"http-nio-20010-exec-21","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:06.874+08:00","@version":"1","message":"TikTok API调用成功，响应码: 0, 消息: Success","logger_name":"com.genco.service.service.impl.AffiliateProductServiceImpl","thread_name":"http-nio-20010-exec-21","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:06.876+08:00","@version":"1","message":"联盟产品搜索成功，返回产品数量: 20","logger_name":"com.genco.admin.controller.AffiliateProductController","thread_name":"http-nio-20010-exec-21","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:06.876+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.AffiliateProductController.searchProducts(AffiliateProductSearchRequest)，prams：[AffiliateProductSearchRequest(pageSize=20, cursor=null, sortField=commission_rate, sortOrder=DESC, titleKeywords=null, salesPriceMin=null, salesPriceMax=null, salesPriceCurrency=null, categoryId=null, commissionRateMin=null, commissionRateMax=null, hasInventory=null, saleRegion=null, unitsSoldMin=null, unitsSoldMax=null)]，cost time：1945245875 ns，cost：1945 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-20010-exec-21","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:06.877+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-21","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:48:06.877+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@345fac2c]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-20010-exec-21","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:48:06.884+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-21","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:06.884+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-20010-exec-21","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:06.884+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-20010-exec-21","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:48:16.915+08:00","@version":"1","message":"开始同步TikTok订单 - pageToken: null, startTime: Thu Jul 31 03:47:16 CST 2025, endTime: Sun Aug 03 03:47:16 CST 2025","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:17.113+08:00","@version":"1","message":"开始调用TikTok API获取订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:17.297+08:00","@version":"1","message":"API调用成功，获取到下一页token: , 开始处理订单数据","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:17.361+08:00","@version":"1","message":"开始处理 0 个订单","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:17.361+08:00","@version":"1","message":"订单批量处理完成 - 总计: 0, 新增: 0, 更新: 0, 跳过: 0, 错误: 0","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:17.486+08:00","@version":"1","message":"TikTok订单同步事务执行完成，下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:17.486+08:00","@version":"1","message":"TikTok订单同步方法执行完成，返回下一页token: ","logger_name":"com.genco.service.service.impl.TiktokOrderSyncServiceImpl","thread_name":"crmeb-scheduled-task-pool-5","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:28.946+08:00","@version":"1","message":"Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7e8dcdaa, started on Sun Aug 03 03:47:10 CST 2025","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"SpringContextShutdownHook","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:48:28.963+08:00","@version":"1","message":"Shutting down ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:28.968+08:00","@version":"1","message":"{dataSource-1} closing ...","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:48:28.972+08:00","@version":"1","message":"{dataSource-1} closed","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:15.638+08:00","@version":"1","message":"Application started with classpath: [file:/Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin/target/classes/, file:/Users/<USER>/.m2/repository/com/genco/genco-service/0.0.1-SNAPSHOT/genco-service-0.0.1-SNAPSHOT.jar, file:/Users/<USER>/.m2/repository/com/genco/genco-common/0.0.1-SNAPSHOT/genco-common-0.0.1-SNAPSHOT.jar, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/Users/<USER>/.m2/repository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/Users/<USER>/.m2/repository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/Users/<USER>/.m2/repository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/Users/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.2.0/mybatis-spring-boot-starter-2.2.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.2.0/mybatis-spring-boot-autoconfigure-2.2.0.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.7/mybatis-3.5.7.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.6/mybatis-spring-2.0.6.jar, file:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/Users/<USER>/.m2/repository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar]","logger_name":"org.springframework.boot.context.logging.ClasspathLoggingApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:15.666+08:00","@version":"1","message":"Starting GencoAdminApplication on mgc-mini.local with PID 48262 (/Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin/target/classes started by mgcis in /Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin)","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:15.666+08:00","@version":"1","message":"The following profiles are active: dev","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:15.666+08:00","@version":"1","message":"Loading source class com.genco.admin.GencoAdminApplication","logger_name":"org.springframework.boot.SpringApplication","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:15.683+08:00","@version":"1","message":"Activated activeProfiles dev","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:15.683+08:00","@version":"1","message":"Profiles already activated, '[prod]' will not be applied","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:15.683+08:00","@version":"1","message":"Loaded config file 'file:/Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin/target/classes/application.yml' (classpath:/application.yml)","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:15.683+08:00","@version":"1","message":"Profiles already activated, '[dev]' will not be applied","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:15.683+08:00","@version":"1","message":"Loaded config file 'file:/Users/<USER>/Workspaces/jingangai.cn/genco/api/genco-admin/target/classes/application-dev.yml' (classpath:/application-dev.yml) for profile dev","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:15.683+08:00","@version":"1","message":"Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@40dff0b7","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:16.146+08:00","@version":"1","message":"Multiple Spring Data modules found, entering strict repository configuration mode!","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.147+08:00","@version":"1","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.162+08:00","@version":"1","message":"Finished Spring Data repository scanning in 8ms. Found 0 Redis repository interfaces.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.393+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.395+08:00","@version":"1","message":"Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.397+08:00","@version":"1","message":"Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@915d7c4' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.398+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.400+08:00","@version":"1","message":"Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.504+08:00","@version":"1","message":"Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:16.504+08:00","@version":"1","message":"Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:16.505+08:00","@version":"1","message":"None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:16.514+08:00","@version":"1","message":"Tomcat initialized with port(s): 20010 (http)","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.517+08:00","@version":"1","message":"Initializing ProtocolHandler [\"http-nio-20010\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.518+08:00","@version":"1","message":"Starting service [Tomcat]","logger_name":"org.apache.catalina.core.StandardService","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.518+08:00","@version":"1","message":"Starting Servlet engine: [Apache Tomcat/9.0.33]","logger_name":"org.apache.catalina.core.StandardEngine","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.605+08:00","@version":"1","message":"At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.","logger_name":"org.apache.jasper.servlet.TldScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.607+08:00","@version":"1","message":"Initializing Spring embedded WebApplicationContext","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.607+08:00","@version":"1","message":"Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:16.607+08:00","@version":"1","message":"Root WebApplicationContext: initialization completed in 924 ms","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:16.874+08:00","@version":"1","message":"Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:16.874+08:00","@version":"1","message":"Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:16.883+08:00","@version":"1","message":"Filter 'webMvcMetricsFilter' configured for use","logger_name":"org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:16.883+08:00","@version":"1","message":"Filter 'requestContextFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:16.883+08:00","@version":"1","message":"Filter 'corsFilter' configured for use","logger_name":"org.springframework.web.filter.CorsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:16.883+08:00","@version":"1","message":"Filter 'characterEncodingFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:16.883+08:00","@version":"1","message":"Filter 'springSecurityFilterChain' configured for use","logger_name":"org.springframework.boot.web.servlet.DelegatingFilterProxyRegistrationBean$1","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:16.883+08:00","@version":"1","message":"Filter 'formContentFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedFormContentFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:18.332+08:00","@version":"1","message":"支付策略初始化完成，支持的支付类型: [offline, weixin, alipay, xendit, haipay, yue]","logger_name":"com.genco.service.service.impl.PaymentStrategyFactory","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:18.771+08:00","@version":"1","message":"289 mappings in 'requestMappingHandlerMapping'","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:18.803+08:00","@version":"1","message":"Exposing 2 endpoint(s) beneath base path '/actuator'","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:18.854+08:00","@version":"1","message":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","logger_name":"springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:18.870+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:18.871+08:00","@version":"1","message":"Initializing ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:18.923+08:00","@version":"1","message":"Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@85b9f9c, org.springframework.security.web.context.SecurityContextPersistenceFilter@309b8144, org.springframework.security.web.header.HeaderWriterFilter@372e972d, org.springframework.web.filter.CorsFilter@5d15789f, org.springframework.web.filter.CorsFilter@5d15789f, org.springframework.web.filter.CorsFilter@5d15789f, org.springframework.security.web.authentication.logout.LogoutFilter@6d5fea64, com.genco.admin.filter.JwtAuthenticationTokenFilter@639948a2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@321558f8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@21abd3a3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@26a004ed, org.springframework.security.web.session.SessionManagementFilter@192b76a4, org.springframework.security.web.access.ExceptionTranslationFilter@53b83897, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@37fc7e3c]","logger_name":"org.springframework.security.web.DefaultSecurityFilterChain","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:18.987+08:00","@version":"1","message":"ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:19.016+08:00","@version":"1","message":"Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:19.025+08:00","@version":"1","message":"ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:19.201+08:00","@version":"1","message":"Context refreshed","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.206+08:00","@version":"1","message":"Found 2 custom documentation plugin(s)","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.217+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.290+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.293+08:00","@version":"1","message":"Generating unique operation named: getByIdsUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.295+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.298+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.301+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.301+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.311+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.312+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.318+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.319+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.325+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.338+08:00","@version":"1","message":"Generating unique operation named: updatePhoneUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.341+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.349+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.351+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.352+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.353+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.354+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.355+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.356+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.359+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.368+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.374+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.377+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.378+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.385+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.392+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.393+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.395+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.396+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.396+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.405+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.408+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.408+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.411+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.413+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.418+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.427+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.432+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.436+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.450+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.456+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.459+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.459+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.461+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.463+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.465+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.466+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.466+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.468+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.468+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.469+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.469+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.471+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.471+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.477+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.477+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.478+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.479+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.479+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.480+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.481+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.481+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.482+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.483+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.483+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.484+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.485+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.486+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.487+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.488+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.497+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.499+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.499+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.500+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.500+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.502+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.503+08:00","@version":"1","message":"Generating unique operation named: getListTreeUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.504+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.505+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.505+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.508+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.511+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.512+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.512+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.512+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.513+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.514+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.515+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.515+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.516+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.516+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.518+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.518+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.519+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.519+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.520+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.522+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.523+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.523+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.525+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.526+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.527+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.528+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.529+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.530+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.531+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.531+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.531+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.532+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.534+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_29","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.534+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.535+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.536+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.536+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.537+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.539+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_30","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.539+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.540+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.540+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.541+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.541+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.542+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_31","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.543+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.543+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.559+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_32","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.559+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.563+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.568+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_33","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.569+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.570+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.570+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.571+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_34","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.571+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.572+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.572+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.574+08:00","@version":"1","message":"Generating unique operation named: getListUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.575+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_35","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.576+08:00","@version":"1","message":"Generating unique operation named: balanceUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.578+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_36","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.578+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.579+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_37","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.579+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.580+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.580+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.586+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.594+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.596+08:00","@version":"1","message":"---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - Sun Aug 03 03:58:19 CST 2025","logger_name":"com.genco.admin.task.tiktok.TiktokTokenRefreshTask","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.596+08:00","@version":"1","message":"Starting ProtocolHandler [\"http-nio-20010\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.613+08:00","@version":"1","message":"Tomcat started on port(s): 20010 (http) with context path ''","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.614+08:00","@version":"1","message":"Started GencoAdminApplication in 4.177 seconds (JVM running for 4.391)","logger_name":"com.genco.admin.GencoAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:19.641+08:00","@version":"1","message":"{dataSource-1} inited","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:21.021+08:00","@version":"1","message":"Tiktok access_token 未到刷新时间，当前时间: 1754164699，过期时间: 1781838424","logger_name":"com.genco.admin.task.tiktok.TiktokTokenRefreshTask","thread_name":"crmeb-scheduled-task-pool-3","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:21.379+08:00","@version":"1","message":"创建TikTok订单拉取初始任务，时间范围：Thu Jul 31 03:58:21 CST 2025 至 Sun Aug 03 03:58:21 CST 2025","logger_name":"com.genco.admin.task.order.OrderTiktokPullTask","thread_name":"crmeb-scheduled-task-pool-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:38.744+08:00","@version":"1","message":"Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@40dff0b7, started on Sun Aug 03 03:58:15 CST 2025","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"SpringContextShutdownHook","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-08-03T03:58:38.749+08:00","@version":"1","message":"Shutting down ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:38.754+08:00","@version":"1","message":"{dataSource-1} closing ...","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
{"@timestamp":"2025-08-03T03:58:38.757+08:00","@version":"1","message":"{dataSource-1} closed","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
