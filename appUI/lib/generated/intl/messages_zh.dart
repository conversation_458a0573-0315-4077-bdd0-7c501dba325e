// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh';

  static String m0(phone) => "验证码已发送至您 ${phone} 的WhatsApp号";

  static String m1(amount) => "立即通过GENCO下单，预计可获得返现Rp ${amount}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about": MessageLookupByLibrary.simpleMessage("关于"),
    "account_empty_hint": MessageLookupByLibrary.simpleMessage("账号不能为空"),
    "activity_rule": MessageLookupByLibrary.simpleMessage("活动规则"),
    "add_to_collection_success": MessageLookupByLibrary.simpleMessage(
      "添加到收藏成功",
    ),
    "agent": MessageLookupByLibrary.simpleMessage("代理"),
    "agent_fee": MessageLookupByLibrary.simpleMessage("代理费"),
    "agree_with_payment_term": MessageLookupByLibrary.simpleMessage(
      "您是否同意支付条款",
    ),
    "all": MessageLookupByLibrary.simpleMessage("全部"),
    "and": MessageLookupByLibrary.simpleMessage("和"),
    "back": MessageLookupByLibrary.simpleMessage("返回"),
    "bank_card_number": MessageLookupByLibrary.simpleMessage("银行卡号"),
    "bank_name": MessageLookupByLibrary.simpleMessage("银行名称"),
    "become_member": MessageLookupByLibrary.simpleMessage("成为代理"),
    "benefit": MessageLookupByLibrary.simpleMessage("权益"),
    "bind_bank_card_confirm": MessageLookupByLibrary.simpleMessage("绑卡确认"),
    "bonus": MessageLookupByLibrary.simpleMessage("奖励"),
    "brand_filter_all": MessageLookupByLibrary.simpleMessage("全部"),
    "brand_filter_latest": MessageLookupByLibrary.simpleMessage("新品"),
    "brand_filter_price": MessageLookupByLibrary.simpleMessage("价格"),
    "brand_filter_rebate_rate": MessageLookupByLibrary.simpleMessage("返现率"),
    "brand_filter_sales": MessageLookupByLibrary.simpleMessage("销量"),
    "brand_high_rebate_title": MessageLookupByLibrary.simpleMessage("高返现品牌"),
    "brand_highest_rebate_rate": MessageLookupByLibrary.simpleMessage("最高返现率"),
    "brand_home_top_subtitle": MessageLookupByLibrary.simpleMessage(
      "邂逅心仪之选的同时畅享超值购物体验",
    ),
    "brand_home_top_title": MessageLookupByLibrary.simpleMessage(
      "<red>严选</red>品质品牌",
    ),
    "brand_tiktok_hot_sale_title": MessageLookupByLibrary.simpleMessage(
      "TikTok热卖品牌",
    ),
    "button_next": MessageLookupByLibrary.simpleMessage("继续"),
    "can_not_open_link": MessageLookupByLibrary.simpleMessage("无法点开链接"),
    "cancel": MessageLookupByLibrary.simpleMessage("取消"),
    "cancel_select_all": MessageLookupByLibrary.simpleMessage("取消全选"),
    "cashback_is_0": MessageLookupByLibrary.simpleMessage("该产品没有返现"),
    "cashback_is_0_content": MessageLookupByLibrary.simpleMessage(
      "该产品没有返现是否继续？",
    ),
    "check_cash_back": MessageLookupByLibrary.simpleMessage("查看现金返还"),
    "check_payment_result": MessageLookupByLibrary.simpleMessage("正在查看付款结果"),
    "collection": MessageLookupByLibrary.simpleMessage("收藏"),
    "confirm": MessageLookupByLibrary.simpleMessage("确定"),
    "congratulation_to_add_group": MessageLookupByLibrary.simpleMessage(
      "恭喜你加入",
    ),
    "contact_up": MessageLookupByLibrary.simpleMessage("联系负责人"),
    "copy_success": MessageLookupByLibrary.simpleMessage("成功复制"),
    "credited_rebase_income": MessageLookupByLibrary.simpleMessage("返现订单收入"),
    "cumulative_number_of_invitations": MessageLookupByLibrary.simpleMessage(
      "累计邀请数量",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("删除"),
    "delete_account": MessageLookupByLibrary.simpleMessage("注销账户"),
    "delete_account_content": MessageLookupByLibrary.simpleMessage(
      "账户注销后您所有的相关权益将全部丢失，\n并且无法再次登录。",
    ),
    "delete_account_title": MessageLookupByLibrary.simpleMessage("确定要注销账户?"),
    "detail_brand_product_amount": MessageLookupByLibrary.simpleMessage("件商品"),
    "detail_brand_product_amount_pre": MessageLookupByLibrary.simpleMessage(
      "共",
    ),
    "detail_cashback_amount": MessageLookupByLibrary.simpleMessage("预计返现金额"),
    "detail_cashback_flow_check": MessageLookupByLibrary.simpleMessage("查看教程"),
    "detail_cashback_flow_step1": MessageLookupByLibrary.simpleMessage("点击产品"),
    "detail_cashback_flow_step2": MessageLookupByLibrary.simpleMessage(
      "在TikTok上\n订购商品",
    ),
    "detail_cashback_flow_step3": MessageLookupByLibrary.simpleMessage("跟踪订单"),
    "detail_cashback_flow_step4": MessageLookupByLibrary.simpleMessage(
      "获取现金\n返还",
    ),
    "detail_cashback_flow_title": MessageLookupByLibrary.simpleMessage(
      "赚取现金返现流程",
    ),
    "detail_price_title": MessageLookupByLibrary.simpleMessage("价格"),
    "detail_rebate_rate": MessageLookupByLibrary.simpleMessage("返现率"),
    "detail_sold": MessageLookupByLibrary.simpleMessage("已售"),
    "detail_sold_count": MessageLookupByLibrary.simpleMessage("件"),
    "diamond": MessageLookupByLibrary.simpleMessage("钻石"),
    "diamond_agent": MessageLookupByLibrary.simpleMessage("钻石代理"),
    "diamond_partner": MessageLookupByLibrary.simpleMessage("钻石合作伙伴"),
    "direct_invite_detail": MessageLookupByLibrary.simpleMessage(
      "每邀请 1 位代理，奖励 35,000 印尼盾，邀请3人直接回本！",
    ),
    "direct_invite_detail2": MessageLookupByLibrary.simpleMessage(
      "每邀请 1 位代理商，奖励 200.000 印尼盾，邀请 3 人即可立即回本！",
    ),
    "direct_invite_detail3": MessageLookupByLibrary.simpleMessage(
      "邀请代理，奖励 35.000 印尼盾 / 人",
    ),
    "direct_invite_reward": MessageLookupByLibrary.simpleMessage("直接邀请奖励"),
    "e_wallet": MessageLookupByLibrary.simpleMessage("电子钱包"),
    "edit": MessageLookupByLibrary.simpleMessage("编辑"),
    "error_button_title": MessageLookupByLibrary.simpleMessage("重试"),
    "error_title": MessageLookupByLibrary.simpleMessage("糟糕！出了些问题。请稍后重试。"),
    "exclusive_benefits": MessageLookupByLibrary.simpleMessage("专属权益"),
    "expenditure": MessageLookupByLibrary.simpleMessage("支出"),
    "extra_bonus": MessageLookupByLibrary.simpleMessage("额外奖励"),
    "extra_cashback": MessageLookupByLibrary.simpleMessage("额外现金返还"),
    "extra_cashback_detail": MessageLookupByLibrary.simpleMessage(
      "在特定时间内享受高现金返还的额外现金返还福利",
    ),
    "extra_cashback_detail_gold": MessageLookupByLibrary.simpleMessage(
      "每发展 10 位黄金代理：奖励 300.000 印尼盾",
    ),
    "feedback_cash": MessageLookupByLibrary.simpleMessage("返现现金"),
    "find_product": MessageLookupByLibrary.simpleMessage("找到商品"),
    "finish": MessageLookupByLibrary.simpleMessage("完成"),
    "gold": MessageLookupByLibrary.simpleMessage("金牌"),
    "gold_agent": MessageLookupByLibrary.simpleMessage("金牌代理"),
    "gold_partner": MessageLookupByLibrary.simpleMessage("金牌合作伙伴"),
    "group": MessageLookupByLibrary.simpleMessage("的团队"),
    "guide_step1_content_flow_1": MessageLookupByLibrary.simpleMessage(
      "在 Shopee 或 TikTok 上打开商品并复制商品链接",
    ),
    "guide_step1_content_flow_1_description":
        MessageLookupByLibrary.simpleMessage(
          "在 Shopee 或 TikTok 中选择产品，点击 “分享” ，选择复制产品链接。",
        ),
    "guide_step1_content_flow_1_title": MessageLookupByLibrary.simpleMessage(
      "复制产品链接",
    ),
    "guide_step1_content_flow_2": MessageLookupByLibrary.simpleMessage(
      "在 GENCO 查看现金返还",
    ),
    "guide_step1_content_flow_2_description":
        MessageLookupByLibrary.simpleMessage(
          "打开GENCO，现金返还金额将自动显示，或者将链接粘贴到搜索栏中。",
        ),
    "guide_step1_content_flow_2_title": MessageLookupByLibrary.simpleMessage(
      "在GENCO查看现金返还",
    ),
    "guide_step1_content_flow_3": MessageLookupByLibrary.simpleMessage(
      "前往 Shopee/TikTok完成下单",
    ),
    "guide_step1_content_flow_3_description":
        MessageLookupByLibrary.simpleMessage(
          "在商品详情中点击 “在 Shopee/TikTok 下单”，跳转到 Shopee/TikTok 后，点击 “立即购买”。",
        ),
    "guide_step1_content_flow_3_title": MessageLookupByLibrary.simpleMessage(
      "点击 “在 Shopee/TikTok 下单” 并进行订购",
    ),
    "guide_step1_content_flow_4": MessageLookupByLibrary.simpleMessage(
      "确认订单完成，获取现金返还",
    ),
    "guide_step1_content_flow_4_description":
        MessageLookupByLibrary.simpleMessage(
          "系统会在 1 - 3 天内追踪你的订单。在 Shopee/TikTok 上点击 “订单已收货” 后，现金返还会直接到账！",
        ),
    "guide_step1_content_flow_4_title": MessageLookupByLibrary.simpleMessage(
      "追踪并领取你的现金返还",
    ),
    "guide_step1_content_flow_5_description":
        MessageLookupByLibrary.simpleMessage("观看教程视频,点击播放并学习详细步骤！"),
    "guide_step1_content_flow_5_title": MessageLookupByLibrary.simpleMessage(
      "如何领取现金返还？",
    ),
    "guide_step1_title": MessageLookupByLibrary.simpleMessage("四步获取现金返还"),
    "high_cashback": MessageLookupByLibrary.simpleMessage("高额返现"),
    "high_cashback_description": MessageLookupByLibrary.simpleMessage(
      "消费越多，省得越多",
    ),
    "home_cashback_button_title": MessageLookupByLibrary.simpleMessage(
      "预计返现金额",
    ),
    "home_cashback_instructions_check_all":
        MessageLookupByLibrary.simpleMessage("查看全部"),
    "home_cashback_instructions_step1": MessageLookupByLibrary.simpleMessage(
      "复制商品\n链接",
    ),
    "home_cashback_instructions_step2": MessageLookupByLibrary.simpleMessage(
      "打开GENCO\n查看返现",
    ),
    "home_cashback_instructions_step3": MessageLookupByLibrary.simpleMessage(
      "前往Shopee\n/TikTok",
    ),
    "home_cashback_instructions_step4": MessageLookupByLibrary.simpleMessage(
      "获取返现",
    ),
    "home_cashback_instructions_title": MessageLookupByLibrary.simpleMessage(
      "赚取现金返现流程",
    ),
    "home_logo_slogan": MessageLookupByLibrary.simpleMessage("买前搜搜，件件返利"),
    "home_navigation_brand": MessageLookupByLibrary.simpleMessage("品牌"),
    "home_navigation_home": MessageLookupByLibrary.simpleMessage("首页"),
    "home_navigation_income": MessageLookupByLibrary.simpleMessage("收入"),
    "home_navigation_mine": MessageLookupByLibrary.simpleMessage("我的"),
    "home_platform_all": MessageLookupByLibrary.simpleMessage("全部"),
    "home_platform_high_rebate": MessageLookupByLibrary.simpleMessage("高返现"),
    "home_platform_hot_sale": MessageLookupByLibrary.simpleMessage("热门"),
    "home_platform_shopee": MessageLookupByLibrary.simpleMessage("Shopee"),
    "home_platform_tiktok": MessageLookupByLibrary.simpleMessage("TikTok"),
    "home_rebate_rate_title": MessageLookupByLibrary.simpleMessage("返现率"),
    "home_search_button_title": MessageLookupByLibrary.simpleMessage("粘贴"),
    "home_search_instructions_copy": MessageLookupByLibrary.simpleMessage("复制"),
    "home_search_instructions_text": MessageLookupByLibrary.simpleMessage(
      "商品链接打开GENCO，拿返利",
    ),
    "home_search_placeholder": MessageLookupByLibrary.simpleMessage(
      "复制商品链接，拿返利",
    ),
    "income": MessageLookupByLibrary.simpleMessage("收入"),
    "income_actual_credited_amount": MessageLookupByLibrary.simpleMessage(
      "返现到账总金额",
    ),
    "income_actual_credited_amount_hint": MessageLookupByLibrary.simpleMessage(
      "订单成功确认完成后，计算得出的\n现金返还总金额。",
    ),
    "income_amount_available_for_withdrawal":
        MessageLookupByLibrary.simpleMessage("可提现金额"),
    "income_amount_credited": MessageLookupByLibrary.simpleMessage("已入账金额"),
    "income_amount_credited_description": MessageLookupByLibrary.simpleMessage(
      "已成功到账的金额",
    ),
    "income_amount_to_be_credited": MessageLookupByLibrary.simpleMessage(
      "待入账金额",
    ),
    "income_amount_to_be_credited_hint": MessageLookupByLibrary.simpleMessage(
      "待入账收入主要包括已成功下单但尚未确认收货的订单返现收入，该金额仅作为参考或预估用途。",
    ),
    "income_campaign_reward": MessageLookupByLibrary.simpleMessage("活动奖励"),
    "income_expected_total_amount": MessageLookupByLibrary.simpleMessage(
      "预计返现总金额",
    ),
    "income_expected_total_amount_hint": MessageLookupByLibrary.simpleMessage(
      "这是您已付款订单的预计现金返还总额。如果发生退款或退货，此预估金额可能会发生变化。\n现金返还预估仅供参考，最终金额将根据您支付的最终价格确定。",
    ),
    "income_income_detail": MessageLookupByLibrary.simpleMessage("收入明细"),
    "income_my_order": MessageLookupByLibrary.simpleMessage("我的订单"),
    "income_order_rebate": MessageLookupByLibrary.simpleMessage("订单返利"),
    "income_pre_total_income": MessageLookupByLibrary.simpleMessage("预计总收入"),
    "income_pre_total_income_description": MessageLookupByLibrary.simpleMessage(
      "预计总收入是已收到和未收到收入的预估，仅供参考，最终金额以实际支付价格为准。",
    ),
    "income_today": MessageLookupByLibrary.simpleMessage("今日收入"),
    "income_transaction_detail": MessageLookupByLibrary.simpleMessage("交易明细"),
    "income_transaction_history": MessageLookupByLibrary.simpleMessage("交易记录"),
    "income_transaction_history_empty": MessageLookupByLibrary.simpleMessage(
      "暂无交易记录",
    ),
    "income_withdrawal_amount": MessageLookupByLibrary.simpleMessage("提现金额"),
    "income_withdrawal_amount_hint": MessageLookupByLibrary.simpleMessage(
      "请输入提现金额",
    ),
    "income_withdrawal_button": MessageLookupByLibrary.simpleMessage("提现"),
    "income_withdrawal_failed": MessageLookupByLibrary.simpleMessage("提现失败"),
    "income_withdrawal_success": MessageLookupByLibrary.simpleMessage("提现成功"),
    "input_bank_card_number": MessageLookupByLibrary.simpleMessage("输入银行卡号"),
    "input_invite_code": MessageLookupByLibrary.simpleMessage("输入邀请码"),
    "input_opt_verification_code": MessageLookupByLibrary.simpleMessage(
      "请输入OTP验证码",
    ),
    "input_opt_verification_code_error": MessageLookupByLibrary.simpleMessage(
      "请输入收到的验证码",
    ),
    "input_opt_verification_code_hint": m0,
    "input_password_hint": MessageLookupByLibrary.simpleMessage("输入登录密码"),
    "invite_agent": MessageLookupByLibrary.simpleMessage("邀请代理"),
    "invite_and_earn_money": MessageLookupByLibrary.simpleMessage("邀请并赚取收益"),
    "invite_and_eran_bonus": MessageLookupByLibrary.simpleMessage("邀请并赚取收益"),
    "invite_bonus": MessageLookupByLibrary.simpleMessage("邀请奖金"),
    "invite_code": MessageLookupByLibrary.simpleMessage("邀请码"),
    "invite_code_empty_hint": MessageLookupByLibrary.simpleMessage("邀请码不能为空!"),
    "invite_diamond_agent": MessageLookupByLibrary.simpleMessage("邀请钻石代理"),
    "invite_gold_agent": MessageLookupByLibrary.simpleMessage("邀请金牌代理"),
    "invite_normal_user": MessageLookupByLibrary.simpleMessage("邀请普通用户"),
    "invite_time": MessageLookupByLibrary.simpleMessage("邀请时间"),
    "invite_to_upgrade": MessageLookupByLibrary.simpleMessage(
      "邀请10位朋友成为银牌代理或更高等级",
    ),
    "invite_to_upgrade_empty": MessageLookupByLibrary.simpleMessage(
      "邀请 10 位新朋友加入成为白银代理或更高等级。\\n你将自动升级状态！",
    ),
    "jump_link_failed": MessageLookupByLibrary.simpleMessage("跳转失败"),
    "jump_to_tiktok": MessageLookupByLibrary.simpleMessage("即将跳转至 TikTok"),
    "level_status": MessageLookupByLibrary.simpleMessage("等级状态"),
    "level_up_bonus": MessageLookupByLibrary.simpleMessage("升级进度与奖励"),
    "level_up_content_title": MessageLookupByLibrary.simpleMessage(
      "邀请10位朋友成为银牌代理或更高等级",
    ),
    "level_up_description": MessageLookupByLibrary.simpleMessage("升级说明"),
    "level_up_description_title": MessageLookupByLibrary.simpleMessage("印尼盾到手"),
    "level_up_description_title1": MessageLookupByLibrary.simpleMessage(
      "只需发展 10 个钻石代理！",
    ),
    "level_up_schedule": MessageLookupByLibrary.simpleMessage("升级进度"),
    "loading_more_empty": MessageLookupByLibrary.simpleMessage("还没有壁纸哦 ~"),
    "loading_more_error": MessageLookupByLibrary.simpleMessage("加载失败，请重试"),
    "loading_more_no_more": MessageLookupByLibrary.simpleMessage("已经到底了哦~"),
    "loading_more_retry": MessageLookupByLibrary.simpleMessage("重试"),
    "loading_more_write": MessageLookupByLibrary.simpleMessage("写评分"),
    "login": MessageLookupByLibrary.simpleMessage("登录"),
    "login_agreement": MessageLookupByLibrary.simpleMessage("登录账户即表示您已阅读并同意"),
    "login_button": MessageLookupByLibrary.simpleMessage("登录"),
    "login_code_hint": MessageLookupByLibrary.simpleMessage("请输入验证码"),
    "login_code_label": MessageLookupByLibrary.simpleMessage("验证码"),
    "login_code_seconds": MessageLookupByLibrary.simpleMessage("秒"),
    "login_code_sent": MessageLookupByLibrary.simpleMessage("验证码已发送"),
    "login_enter_phone": MessageLookupByLibrary.simpleMessage("请输入手机号"),
    "login_enter_phone_code": MessageLookupByLibrary.simpleMessage(
      "请输入手机号和验证码",
    ),
    "login_expired_hint": MessageLookupByLibrary.simpleMessage("登录已经过期，请重新登陆!"),
    "login_failed": MessageLookupByLibrary.simpleMessage("登录失败"),
    "login_get_code": MessageLookupByLibrary.simpleMessage("获取验证码"),
    "login_mobile_subtitle": MessageLookupByLibrary.simpleMessage("请使用手机号登录"),
    "login_mobile_title": MessageLookupByLibrary.simpleMessage("手机登录"),
    "login_password": MessageLookupByLibrary.simpleMessage("登录密码"),
    "login_password_alternative": MessageLookupByLibrary.simpleMessage(
      "使用账号密码登录",
    ),
    "login_password_confirm": MessageLookupByLibrary.simpleMessage("确认登录密码"),
    "login_password_confirm_hint": MessageLookupByLibrary.simpleMessage(
      "再次输入登录密码",
    ),
    "login_phone_hint": MessageLookupByLibrary.simpleMessage("请输入手机号码"),
    "login_phone_label": MessageLookupByLibrary.simpleMessage("手机号"),
    "login_send_failed": MessageLookupByLibrary.simpleMessage("验证码发送失败"),
    "login_subtitle": MessageLookupByLibrary.simpleMessage("购物省钱，分享赚钱"),
    "login_success": MessageLookupByLibrary.simpleMessage("登录成功"),
    "login_title": MessageLookupByLibrary.simpleMessage("欢迎来到GENCO"),
    "login_welcome_back": MessageLookupByLibrary.simpleMessage("欢迎回来"),
    "login_with_other_method": MessageLookupByLibrary.simpleMessage("使用其他方式登录"),
    "login_with_password": MessageLookupByLibrary.simpleMessage("密码登录"),
    "login_with_tiktok": MessageLookupByLibrary.simpleMessage("TikTok登录"),
    "login_with_verification_code": MessageLookupByLibrary.simpleMessage(
      "验证码登录",
    ),
    "logout": MessageLookupByLibrary.simpleMessage("退出登录"),
    "logout_confirm_message": MessageLookupByLibrary.simpleMessage("确定要退出登录吗？"),
    "logout_confirm_title": MessageLookupByLibrary.simpleMessage("确认退出"),
    "member_benefits_partner_agent_1": MessageLookupByLibrary.simpleMessage(
      "有效期",
    ),
    "member_benefits_partner_agent_1_value":
        MessageLookupByLibrary.simpleMessage("永久"),
    "member_benefits_partner_agent_2": MessageLookupByLibrary.simpleMessage(
      "邀请奖金",
    ),
    "member_benefits_partner_agent_2_value":
        MessageLookupByLibrary.simpleMessage("成功后可获得高额奖金10亿+"),
    "member_benefits_partner_agent_3": MessageLookupByLibrary.simpleMessage(
      "团队购物佣金",
    ),
    "member_benefits_partner_agent_3_value":
        MessageLookupByLibrary.simpleMessage("从下线的每笔现金返还中获得最高 20%返现"),
    "member_benefits_partner_agent_4": MessageLookupByLibrary.simpleMessage(
      "额外现金返还",
    ),
    "member_benefits_partner_agent_4_value":
        MessageLookupByLibrary.simpleMessage("现金返还更高，最高可达 100%"),
    "member_benefits_silver_agent_1": MessageLookupByLibrary.simpleMessage(
      "有效期",
    ),
    "member_benefits_silver_agent_1_value":
        MessageLookupByLibrary.simpleMessage("1年"),
    "member_benefits_silver_agent_2": MessageLookupByLibrary.simpleMessage(
      "邀请奖金",
    ),
    "member_benefits_silver_agent_2_benefit":
        MessageLookupByLibrary.simpleMessage("可获得高达10.000.000印尼盾奖金"),
    "member_benefits_silver_agent_2_value":
        MessageLookupByLibrary.simpleMessage("成功邀人，即可获额外奖励"),
    "member_benefits_silver_agent_3": MessageLookupByLibrary.simpleMessage(
      "团队购物佣金",
    ),
    "member_benefits_silver_agent_3_benefit":
        MessageLookupByLibrary.simpleMessage(
          "直接推荐的购物现金返还的10%佣金（5%常规佣金+5%活动补贴） ",
        ),
    "member_benefits_silver_agent_3_value":
        MessageLookupByLibrary.simpleMessage("团队消费，你享佣金收益"),
    "member_benefits_silver_agent_4": MessageLookupByLibrary.simpleMessage(
      "额外现金返还",
    ),
    "member_benefits_silver_agent_4_benefit":
        MessageLookupByLibrary.simpleMessage(
          "直接推荐的购物现金返还的10%佣金（5%常规佣金+5%活动补贴） ",
        ),
    "member_benefits_silver_agent_4_value":
        MessageLookupByLibrary.simpleMessage("现金返还更高，最高可达 50%"),
    "member_benefits_silver_agent_5": MessageLookupByLibrary.simpleMessage(
      "无限制现金返还",
    ),
    "member_benefits_silver_agent_5_benefit":
        MessageLookupByLibrary.simpleMessage(
          "直接推荐的购物现金返还的10%佣金（5%常规佣金+5%活动补贴） ",
        ),
    "member_benefits_silver_agent_5_value":
        MessageLookupByLibrary.simpleMessage("现金返还无限制"),
    "member_introduction": MessageLookupByLibrary.simpleMessage(
      "升级成为代理或者合作伙伴赚取更多收益",
    ),
    "member_introduction_level_silver_agent":
        MessageLookupByLibrary.simpleMessage("银牌代理"),
    "member_level_partner": MessageLookupByLibrary.simpleMessage("合作伙伴"),
    "member_level_partner_fee": MessageLookupByLibrary.simpleMessage("合作伙伴费用"),
    "member_level_silver_agent": MessageLookupByLibrary.simpleMessage("银色代理"),
    "member_level_silver_agent_fee": MessageLookupByLibrary.simpleMessage(
      "银色代理费用",
    ),
    "member_status_description": MessageLookupByLibrary.simpleMessage(
      "您当前未开通代理或者合作伙伴",
    ),
    "message_no_data": MessageLookupByLibrary.simpleMessage("暂无数据"),
    "modify_nickname": MessageLookupByLibrary.simpleMessage("修改昵称"),
    "modify_password": MessageLookupByLibrary.simpleMessage("修改登录密码"),
    "modify_phone_number": MessageLookupByLibrary.simpleMessage("修改手机号"),
    "modify_success": MessageLookupByLibrary.simpleMessage("修改成功"),
    "my_avatar": MessageLookupByLibrary.simpleMessage("我的头像"),
    "my_collection": MessageLookupByLibrary.simpleMessage("我的收藏"),
    "my_team": MessageLookupByLibrary.simpleMessage("我的团队"),
    "name": MessageLookupByLibrary.simpleMessage("姓名"),
    "name_placeholder": MessageLookupByLibrary.simpleMessage("输入姓名"),
    "network_error": MessageLookupByLibrary.simpleMessage("网络错误"),
    "network_is_not_available": MessageLookupByLibrary.simpleMessage(
      "网络不可用，请检查网络连接",
    ),
    "next_step": MessageLookupByLibrary.simpleMessage("下一步"),
    "nickname": MessageLookupByLibrary.simpleMessage("昵称"),
    "nickname_hint": MessageLookupByLibrary.simpleMessage("请输入昵称"),
    "nickname_too_long": MessageLookupByLibrary.simpleMessage("昵称太长了，请在十个字符以内"),
    "no_limit": MessageLookupByLibrary.simpleMessage("无限制"),
    "no_limit_description": MessageLookupByLibrary.simpleMessage("畅享无限制现金返现"),
    "normal_member": MessageLookupByLibrary.simpleMessage("普通会员"),
    "normal_member_user": MessageLookupByLibrary.simpleMessage("普通用户"),
    "normal_user": MessageLookupByLibrary.simpleMessage("普通用户"),
    "normal_user_2_benefit": MessageLookupByLibrary.simpleMessage("无奖金"),
    "normal_user_3_benefit": MessageLookupByLibrary.simpleMessage("无购物佣金"),
    "normal_user_4_benefit": MessageLookupByLibrary.simpleMessage("无购物佣金"),
    "normal_user_5_benefit": MessageLookupByLibrary.simpleMessage("无购物佣金"),
    "ok": MessageLookupByLibrary.simpleMessage("确认"),
    "open_payment_link": MessageLookupByLibrary.simpleMessage("直接打开支付链接"),
    "order_application_time": MessageLookupByLibrary.simpleMessage("申请时间："),
    "order_cashback_info": MessageLookupByLibrary.simpleMessage("返现说明"),
    "order_expected_cashback": MessageLookupByLibrary.simpleMessage("预计返现金额："),
    "order_payment": MessageLookupByLibrary.simpleMessage("订单支付"),
    "order_price": MessageLookupByLibrary.simpleMessage("订单金额"),
    "order_right_now": MessageLookupByLibrary.simpleMessage("立即下单"),
    "order_status_completed": MessageLookupByLibrary.simpleMessage("已完成"),
    "order_status_expired": MessageLookupByLibrary.simpleMessage("已过期"),
    "order_status_processing": MessageLookupByLibrary.simpleMessage("正在处理"),
    "order_tab_all": MessageLookupByLibrary.simpleMessage("全部"),
    "order_tab_completed": MessageLookupByLibrary.simpleMessage("已完成"),
    "order_tab_expired": MessageLookupByLibrary.simpleMessage("已过期"),
    "order_tab_processing": MessageLookupByLibrary.simpleMessage("处理中"),
    "order_title": MessageLookupByLibrary.simpleMessage("我的订单"),
    "partner": MessageLookupByLibrary.simpleMessage("合作伙伴"),
    "partner_extra_bonus1": MessageLookupByLibrary.simpleMessage(
      "每发展 10 位银牌合作伙伴：奖励 1.000.000 印尼盾",
    ),
    "partner_extra_bonus2": MessageLookupByLibrary.simpleMessage(
      "每发展 10 位黄金合作伙伴：奖励 2.000.000 印尼盾",
    ),
    "partner_extra_bonus3": MessageLookupByLibrary.simpleMessage(
      "每发展 10 名金牌合作伙伴：奖金 2.000.000印尼盾；\n每发展 10 名钻石合作伙伴：奖金 100.000.000印尼盾.",
    ),
    "password_not_same": MessageLookupByLibrary.simpleMessage("两次输入的密码不一致"),
    "pay_with_qrcode": MessageLookupByLibrary.simpleMessage("使用二维码支付"),
    "pay_with_qrcode_usage": MessageLookupByLibrary.simpleMessage(
      "扫描二维码并打开支付链接支付，如果您想直接在本机支付请直接点击打开链接支付",
    ),
    "payment_agreement": MessageLookupByLibrary.simpleMessage("阅读并同意"),
    "payment_agreement_link": MessageLookupByLibrary.simpleMessage("支付条款"),
    "payment_amount": MessageLookupByLibrary.simpleMessage("付款金额"),
    "payment_complete": MessageLookupByLibrary.simpleMessage("已付款完成"),
    "payment_failed": MessageLookupByLibrary.simpleMessage("付款失败"),
    "payment_id": MessageLookupByLibrary.simpleMessage("付款流水号"),
    "payment_method": MessageLookupByLibrary.simpleMessage("付款方式"),
    "payment_problem": MessageLookupByLibrary.simpleMessage("付款遇到问题"),
    "payment_success": MessageLookupByLibrary.simpleMessage("付款成功"),
    "phone_number": MessageLookupByLibrary.simpleMessage("手机号"),
    "phone_number_placeholder": MessageLookupByLibrary.simpleMessage("输入手机号"),
    "please_choose_payment_method": MessageLookupByLibrary.simpleMessage(
      "请选择支付方式",
    ),
    "please_input_amount": MessageLookupByLibrary.simpleMessage("请输入提现金额"),
    "please_input_bank_number": MessageLookupByLibrary.simpleMessage("请输入银行卡号"),
    "please_input_e_wallet_account": MessageLookupByLibrary.simpleMessage(
      "请输入电子钱包账号",
    ),
    "please_input_your_password": MessageLookupByLibrary.simpleMessage(
      "请输入您的登录密码",
    ),
    "please_select_bank": MessageLookupByLibrary.simpleMessage("请选择银行"),
    "please_select_bank_or_e_wallet": MessageLookupByLibrary.simpleMessage(
      "请选择银行或者电子钱包",
    ),
    "please_select_e_wallet": MessageLookupByLibrary.simpleMessage("请输入电子钱包"),
    "please_select_withdrawal_account": MessageLookupByLibrary.simpleMessage(
      "请选择提现账户",
    ),
    "pre_team_cashback": MessageLookupByLibrary.simpleMessage("预计团队贡献现金返还"),
    "privacy": MessageLookupByLibrary.simpleMessage("隐私政策"),
    "privacy_policy": MessageLookupByLibrary.simpleMessage("隐私政策"),
    "product_link_empty": MessageLookupByLibrary.simpleMessage("没找链接对应的商品"),
    "product_link_empty_content": MessageLookupByLibrary.simpleMessage(
      "该商品链接未找到对应的商品，请检查链接是否正确，或尝试其他商品。",
    ),
    "product_name": MessageLookupByLibrary.simpleMessage("产品名称"),
    "purchase_right_now": MessageLookupByLibrary.simpleMessage("立即支付"),
    "qrcode": MessageLookupByLibrary.simpleMessage("二维码"),
    "real_payment_price": MessageLookupByLibrary.simpleMessage("实际支付金额"),
    "rebase_cash": MessageLookupByLibrary.simpleMessage("获得返现"),
    "rebase_expenditure": MessageLookupByLibrary.simpleMessage("返现支出"),
    "rebase_income": MessageLookupByLibrary.simpleMessage("返现收入"),
    "rebase_info": MessageLookupByLibrary.simpleMessage("返现详情"),
    "rebase_price": MessageLookupByLibrary.simpleMessage("购买价格"),
    "rebase_rate": MessageLookupByLibrary.simpleMessage("返现率"),
    "rebate_genco_last_app_content": MessageLookupByLibrary.simpleMessage(
      "购物前，请确保我们的应用程序是您最后点击的应用程序。如果您在打开我们的应用程序后访问其他网站或应用程序，将无法获得返现。",
    ),
    "rebate_genco_last_app_title": MessageLookupByLibrary.simpleMessage(
      "GENCO是您最后打开的应用程序",
    ),
    "rebate_how_to_get_title": MessageLookupByLibrary.simpleMessage(
      "以下是在 Shopee 和 TikTok Shop 购物时获得返现的方法：",
    ),
    "rebate_how_to_order_content": MessageLookupByLibrary.simpleMessage(
      "从我们支持的第三方平台复制产品链接后，请务必通过我们的应用程序下单，这将把您重定向回第三方平台。如果您想订购 3 种不同的产品，每个产品都必须通过我们的应用程序打开，以便每个产品订单都能被记录。",
    ),
    "rebate_how_to_order_title": MessageLookupByLibrary.simpleMessage("如何下单"),
    "rebate_instruction_content": MessageLookupByLibrary.simpleMessage(
      "完成订单后，我们将在 1–3 天内跟踪您的订单状态。在您确认收到货物且订单完成后，现金将退还至您的账户余额。",
    ),
    "rebate_instruction_title": MessageLookupByLibrary.simpleMessage("返现说明"),
    "rebate_step_1_content": MessageLookupByLibrary.simpleMessage(
      "通过我们支持的应用程序比如Shopee/TikTok进行产品订购。",
    ),
    "rebate_step_1_title": MessageLookupByLibrary.simpleMessage("01"),
    "rebate_step_2_content": MessageLookupByLibrary.simpleMessage(
      "下单1–3天后跟踪订单状态。",
    ),
    "rebate_step_2_title": MessageLookupByLibrary.simpleMessage("02"),
    "rebate_step_3_content": MessageLookupByLibrary.simpleMessage(
      "确认订单完成后，获得返现。",
    ),
    "rebate_step_3_title": MessageLookupByLibrary.simpleMessage("03"),
    "rebate_unsupported_order_content": MessageLookupByLibrary.simpleMessage(
      "购物前，请确保我们的应用程序是您最后点击的应用程序。如果您在打开我们的应用程序后访问其他网站或应用程序，将无法获得返现。",
    ),
    "rebate_unsupported_order_title": MessageLookupByLibrary.simpleMessage(
      "不支持返现的订单",
    ),
    "received_bonus": MessageLookupByLibrary.simpleMessage("已收到奖金"),
    "resend_code": MessageLookupByLibrary.simpleMessage("重新发送"),
    "resend_in": MessageLookupByLibrary.simpleMessage("重新获取"),
    "role": MessageLookupByLibrary.simpleMessage("角色"),
    "search": MessageLookupByLibrary.simpleMessage("搜索"),
    "seconds": MessageLookupByLibrary.simpleMessage("s"),
    "select_all": MessageLookupByLibrary.simpleMessage("全选"),
    "select_bank": MessageLookupByLibrary.simpleMessage("选择银行"),
    "select_e_wallet": MessageLookupByLibrary.simpleMessage("选择电子钱包"),
    "set_password_hint": MessageLookupByLibrary.simpleMessage(
      "请输入6-20位字母和数字组合的登录密码",
    ),
    "setting": MessageLookupByLibrary.simpleMessage("设置"),
    "setting_login_password": MessageLookupByLibrary.simpleMessage("设置登录密码"),
    "share_text": m1,
    "shopping_bonus": MessageLookupByLibrary.simpleMessage("购物奖金"),
    "silver": MessageLookupByLibrary.simpleMessage("银牌"),
    "silver_agent": MessageLookupByLibrary.simpleMessage("银牌代理"),
    "silver_partner": MessageLookupByLibrary.simpleMessage("银牌合作伙伴"),
    "task_cash_income": MessageLookupByLibrary.simpleMessage("现金收益 (Rp)"),
    "task_center": MessageLookupByLibrary.simpleMessage("任务中心"),
    "task_center_title": MessageLookupByLibrary.simpleMessage("任务中心"),
    "task_close": MessageLookupByLibrary.simpleMessage("关闭"),
    "task_conditions_met": MessageLookupByLibrary.simpleMessage("条件已满足"),
    "task_conditions_not_met": MessageLookupByLibrary.simpleMessage("条件未满足"),
    "task_daily_tasks": MessageLookupByLibrary.simpleMessage("任务列表"),
    "task_developing": MessageLookupByLibrary.simpleMessage("任务功能开发中"),
    "task_feature_developing": MessageLookupByLibrary.simpleMessage("功能开发中"),
    "task_go_claim": MessageLookupByLibrary.simpleMessage("去领取"),
    "task_invite_count": MessageLookupByLibrary.simpleMessage("邀请人数"),
    "task_invite_progress": MessageLookupByLibrary.simpleMessage("邀请进度"),
    "task_invite_reward": MessageLookupByLibrary.simpleMessage("邀新首单奖励"),
    "task_order_count": MessageLookupByLibrary.simpleMessage("完单数量"),
    "task_order_progress": MessageLookupByLibrary.simpleMessage("完单进度"),
    "task_per_completion": MessageLookupByLibrary.simpleMessage("每次完成"),
    "task_record_title": MessageLookupByLibrary.simpleMessage("任务记录"),
    "task_redeemed_invites": MessageLookupByLibrary.simpleMessage("已兑换邀请"),
    "task_redeemed_orders": MessageLookupByLibrary.simpleMessage("已兑换完单"),
    "task_return_cash_welfare": MessageLookupByLibrary.simpleMessage("回归现金福利"),
    "task_return_cash_welfare_desc": MessageLookupByLibrary.simpleMessage(
      "每日专享",
    ),
    "task_reward_amount": MessageLookupByLibrary.simpleMessage("奖励金额"),
    "task_total_invites": MessageLookupByLibrary.simpleMessage("累计邀请人数"),
    "task_total_orders": MessageLookupByLibrary.simpleMessage("累计完单数"),
    "task_view_record": MessageLookupByLibrary.simpleMessage("查看记录"),
    "task_withdraw": MessageLookupByLibrary.simpleMessage("去提现"),
    "task_withdrawable_amount": MessageLookupByLibrary.simpleMessage("可提现金额"),
    "team_bonus": MessageLookupByLibrary.simpleMessage("团队奖励"),
    "team_bonus_detail": MessageLookupByLibrary.simpleMessage(
      "非直接推荐（二级）：每人积累 100.000印尼盾；\n非直接推荐（三级）：每人积累 50.000印尼盾；\n若您达到 “金牌合作伙伴” 状态（成功直接推荐 10 名合作伙伴），奖金可提取，自奖金产生之日起 60 天内有效。若未在该期限内达成，奖金将自动失效。",
    ),
    "team_purchase_bonus": MessageLookupByLibrary.simpleMessage("团队购物佣金"),
    "team_purchase_detail": MessageLookupByLibrary.simpleMessage(
      "丰富收益，享受你直接邀请的团队购物金额的 10% 佣金",
    ),
    "team_purchase_detail_gold": MessageLookupByLibrary.simpleMessage(
      "间接邀请（二级）：每人可累计 15.000 印尼盾；间接邀请（三级）：每人可累计 10.000 印尼盾。",
    ),
    "team_purchase_detail_gold2": MessageLookupByLibrary.simpleMessage(
      "间接邀请（二级）：每人可获得 100.000 印尼盾；间接邀请（三级）：每人可获得 50.000 印尼盾",
    ),
    "team_support": MessageLookupByLibrary.simpleMessage("团队贡献"),
    "to_gold_progress": MessageLookupByLibrary.simpleMessage("成为 金牌代理进度"),
    "today": MessageLookupByLibrary.simpleMessage("今日"),
    "trade_channel": MessageLookupByLibrary.simpleMessage("交易渠道"),
    "trade_order_number": MessageLookupByLibrary.simpleMessage("订单编号"),
    "trade_serial_number": MessageLookupByLibrary.simpleMessage("交易流水号"),
    "trade_time": MessageLookupByLibrary.simpleMessage("交易时间"),
    "trade_type": MessageLookupByLibrary.simpleMessage("交易类型"),
    "training": MessageLookupByLibrary.simpleMessage("培训"),
    "training_detail": MessageLookupByLibrary.simpleMessage(
      "专业导师提供高质量培训课程和综合辅导服务",
    ),
    "unknown_error": MessageLookupByLibrary.simpleMessage("未知错误"),
    "upgrade_date": MessageLookupByLibrary.simpleMessage("升级时间:2025年6月11日"),
    "usage_guideline_description": MessageLookupByLibrary.simpleMessage(
      "通过链接查看现金返还在Shopee和TikTok仅需3步即可获得更多现金返还",
    ),
    "usage_guideline_step1": MessageLookupByLibrary.simpleMessage(
      "在 <red>Shopee</red> 或 <red>TikTok</red> 上打开商品并复制商品链接",
    ),
    "usage_guideline_step2": MessageLookupByLibrary.simpleMessage(
      "返回<red>GENCO</red>，粘贴商品链接，并查看现金返还",
    ),
    "usage_guideline_step3": MessageLookupByLibrary.simpleMessage(
      "从<red>GENCO</red> 跳转至 <red>Shopee</red> 或 <red>TikTok</red> 完成下单",
    ),
    "usage_guideline_title": MessageLookupByLibrary.simpleMessage("使用指南"),
    "usage_hint": MessageLookupByLibrary.simpleMessage("与GENCO一起省钱"),
    "user_agreement": MessageLookupByLibrary.simpleMessage("用户协议"),
    "user_service": MessageLookupByLibrary.simpleMessage("客户服务"),
    "user_service_description": MessageLookupByLibrary.simpleMessage("优质的客户服务"),
    "valid_for": MessageLookupByLibrary.simpleMessage("有效期:1年"),
    "welcome_back": MessageLookupByLibrary.simpleMessage("Hi，欢迎回来"),
    "whatsapp_account": MessageLookupByLibrary.simpleMessage("Whatsapp号"),
    "whatsapp_account_hint": MessageLookupByLibrary.simpleMessage(
      "请输入您的Whatsapp号",
    ),
    "withdrawal_account": MessageLookupByLibrary.simpleMessage("提现账户"),
    "withdrawal_add_card": MessageLookupByLibrary.simpleMessage("添加银行卡"),
    "withdrawal_add_e_card": MessageLookupByLibrary.simpleMessage("添加电子钱包"),
    "withdrawal_all": MessageLookupByLibrary.simpleMessage("全部提现"),
    "withdrawal_amount": MessageLookupByLibrary.simpleMessage("提现金额"),
    "withdrawal_amount_hint": MessageLookupByLibrary.simpleMessage("最大可提现金额"),
    "withdrawal_amount_min": MessageLookupByLibrary.simpleMessage("最低可提现金额"),
    "withdrawal_choose_method": MessageLookupByLibrary.simpleMessage("选择提现账户"),
    "withdrawal_failed": MessageLookupByLibrary.simpleMessage("提现失败"),
    "withdrawal_fees": MessageLookupByLibrary.simpleMessage("手续费"),
    "withdrawal_fees_hint": MessageLookupByLibrary.simpleMessage(
      "手续费为提现金额的 1.5%，系统将按比例计算。最低提现金额为 Rp5.550。若提现金额低于Rp5.550，费用将按 Rp5.550的标准收取。",
    ),
    "withdrawal_finish": MessageLookupByLibrary.simpleMessage("提现完成"),
    "withdrawal_hint": MessageLookupByLibrary.simpleMessage("温馨提示"),
    "withdrawal_hint_description": MessageLookupByLibrary.simpleMessage(
      "提取资金将在 24 小时内到账（周六、周日或节假日除外）",
    ),
    "withdrawal_success": MessageLookupByLibrary.simpleMessage("提现申请已提交完成"),
    "withdrawal_success_hint": MessageLookupByLibrary.simpleMessage(
      "提取资金将在 24 小时内到账(周六、周日或节假日除外)，请随时关注账户资金变化!",
    ),
    "year": MessageLookupByLibrary.simpleMessage("年"),
  };
}
