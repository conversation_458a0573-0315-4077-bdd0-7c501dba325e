# Genco项目推荐关系绑定机制分析摘要

## 核心信息

### 数据库字段（eb_user表）
- **spread_uid**: 推荐人ID
- **spread_count**: 下级人数  
- **spread_time**: 绑定时间
- **path**: 推荐层级路径（当前为"/0/"）
- **invite_code**: 6位邀请码（5字母+1数字）

### 实际数据示例
- uid=6 → uid=3 → uid=21 的推荐链条
- 所有用户path字段统一为"/0/"

### 核心方法（UserServiceImpl.java）
1. **checkBingSpread()**: 验证推荐关系合法性
2. **bindSpread()**: 执行推荐关系绑定
3. **bindInviteCode()**: 邀请码绑定
4. **updateSpreadCountByUid()**: 更新下级数量
5. **clearSpread()**: 清除推荐关系

### API接口
- GET `/api/front/user/bindSpread?spreadPid={id}`: 绑定推广关系
- POST `/api/front/user/bindInviteCode`: 绑定邀请码

### 系统配置
- brokerage_func_status = 1 (分销功能启用)
- store_brokerage_status = 1 (指定分销模式)
- store_brokerage_ratio = 10 (一级返佣10%)
- store_brokerage_two = 5 (二级返佣5%)

### 业务规则
1. 防止循环推荐
2. 用户只能绑定一次推荐关系
3. 支持最多3级推荐
4. 事务安全保证数据一致性

### 绑定触发时机
1. 用户注册时通过spreadUid参数
2. 登录用户主动输入邀请码

### 邀请码生成规则
```java
// 5个大写字母 + 1个数字，随机打乱顺序
public static String generateInviteCode() {
    String letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    String digits = "0123456789";
    // 生成并打乱顺序确保唯一性
}
```

### 关键文件路径
- User实体: `api/genco-common/src/main/java/com/genco/common/model/user/User.java`
- 核心Service: `api/genco-service/src/main/java/com/genco/service/service/impl/UserServiceImpl.java`
- Controller: `api/genco-front/src/main/java/com/genco/front/controller/UserController.java`
- 工具类: `api/genco-common/src/main/java/com/genco/common/utils/CommonUtil.java`

### 数据库连接
- 主机: 35.198.200.73:53306
- 数据库: genco_data
- 主要表: eb_user, eb_system_config

---
**分析完成时间**: 2025-08-02  
**详细文档**: genco_referral_analysis.md
